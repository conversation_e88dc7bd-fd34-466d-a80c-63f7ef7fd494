version: '3.8'

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
  security:
    driver: bridge
  database:
    driver: bridge
    internal: true

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  wazuh_data:
  vault_data:
  grafana_data:
  prometheus_data:

services:
  # ============================================================================
  # SECURITY PERIMETER - WAF & REVERSE PROXY
  # ============================================================================
  
  nginx:
    build: ./nginx
    container_name: trustvault-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    networks:
      - frontend
      - backend
    depends_on:
      - django
      - react
    restart: unless-stopped
    environment:
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.template
    labels:
      - "com.trustvault.service=nginx"
      - "com.trustvault.security.level=high"

  # ModSecurity WAF
  modsecurity:
    image: owasp/modsecurity-crs:nginx
    container_name: trustvault-waf
    ports:
      - "8080:80"
    volumes:
      - ./modsecurity/conf:/etc/modsecurity.d
      - ./logs/modsecurity:/var/log/modsec
    networks:
      - frontend
    environment:
      - PARANOIA=2
      - ANOMALY_INBOUND=5
      - ANOMALY_OUTBOUND=4
    restart: unless-stopped

  # Fail2Ban for DDoS Protection
  fail2ban:
    image: crazymax/fail2ban:latest
    container_name: trustvault-fail2ban
    network_mode: "host"
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      - ./fail2ban:/data
      - ./logs:/var/log:ro
    environment:
      - TZ=Europe/Paris
      - F2B_LOG_LEVEL=INFO
    restart: unless-stopped

  # ============================================================================
  # APPLICATION LAYER
  # ============================================================================
  
  django:
    build: ./backend
    container_name: trustvault-django
    volumes:
      - ./backend:/app
      - ./logs/django:/app/logs
    networks:
      - backend
      - database
      - security
    environment:
      - DEBUG=False
      - SECRET_KEY_FILE=/run/secrets/django_secret
      - DATABASE_URL=postgresql://trustvault:${DB_PASSWORD}@postgres:5432/trustvault
      - REDIS_URL=redis://redis:6379/0
      - VAULT_URL=http://vault:8200
      - WAZUH_API_URL=http://wazuh-manager:55000
    secrets:
      - django_secret
      - db_password
    depends_on:
      - postgres
      - redis
      - vault
    restart: unless-stopped
    labels:
      - "com.trustvault.service=django"
      - "com.trustvault.security.level=high"

  react:
    build: ./frontend
    container_name: trustvault-react
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - frontend
    environment:
      - REACT_APP_API_URL=https://api.trustvault.local
      - REACT_APP_ENVIRONMENT=production
    restart: unless-stopped

  celery:
    build: ./backend
    container_name: trustvault-celery
    command: celery -A trustvault worker -l info
    volumes:
      - ./backend:/app
      - ./logs/celery:/app/logs
    networks:
      - backend
      - database
    environment:
      - DATABASE_URL=postgresql://trustvault:${DB_PASSWORD}@postgres:5432/trustvault
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # ============================================================================
  # DATA LAYER - ENCRYPTED DATABASES
  # ============================================================================
  
  postgres:
    image: postgres:15-alpine
    container_name: trustvault-postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
      - ./postgres/ssl:/var/lib/postgresql/ssl
    networks:
      - database
    environment:
      - POSTGRES_DB=trustvault
      - POSTGRES_USER=trustvault
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    secrets:
      - db_password
    command: >
      postgres
      -c ssl=on
      -c ssl_cert_file=/var/lib/postgresql/ssl/server.crt
      -c ssl_key_file=/var/lib/postgresql/ssl/server.key
      -c ssl_ca_file=/var/lib/postgresql/ssl/ca.crt
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
    restart: unless-stopped
    labels:
      - "com.trustvault.service=postgres"
      - "com.trustvault.security.level=critical"

  redis:
    image: redis:7-alpine
    container_name: trustvault-redis
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - backend
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped

  # HashiCorp Vault for Secrets Management
  vault:
    image: vault:latest
    container_name: trustvault-vault
    cap_add:
      - IPC_LOCK
    volumes:
      - vault_data:/vault/data
      - ./vault/config:/vault/config
    networks:
      - backend
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=${VAULT_ROOT_TOKEN}
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
    command: vault server -config=/vault/config/vault.hcl
    restart: unless-stopped

  # ============================================================================
  # SECURITY & MONITORING - SIEM/SOC
  # ============================================================================
  
  # Wazuh Manager (SIEM)
  wazuh-manager:
    image: wazuh/wazuh-manager:4.7.0
    container_name: trustvault-wazuh-manager
    hostname: wazuh-manager
    volumes:
      - wazuh_data:/var/ossec/data
      - ./wazuh/config:/wazuh-config-mount
      - ./logs:/var/log/external:ro
    networks:
      - security
    environment:
      - INDEXER_URL=https://wazuh-indexer:9200
      - INDEXER_USERNAME=admin
      - INDEXER_PASSWORD=${WAZUH_PASSWORD}
      - FILEBEAT_SSL_VERIFICATION_MODE=full
    restart: unless-stopped

  # Wazuh Indexer (Elasticsearch)
  wazuh-indexer:
    image: wazuh/wazuh-indexer:4.7.0
    container_name: trustvault-wazuh-indexer
    hostname: wazuh-indexer
    volumes:
      - elasticsearch_data:/var/lib/wazuh-indexer
      - ./wazuh/indexer:/usr/share/wazuh-indexer/config
    networks:
      - security
    environment:
      - "OPENSEARCH_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    restart: unless-stopped

  # Wazuh Dashboard
  wazuh-dashboard:
    image: wazuh/wazuh-dashboard:4.7.0
    container_name: trustvault-wazuh-dashboard
    hostname: wazuh-dashboard
    ports:
      - "5601:5601"
    networks:
      - security
      - frontend
    environment:
      - INDEXER_USERNAME=admin
      - INDEXER_PASSWORD=${WAZUH_PASSWORD}
      - WAZUH_API_URL=https://wazuh-manager
      - API_USERNAME=wazuh-wui
      - API_PASSWORD=${WAZUH_API_PASSWORD}
    depends_on:
      - wazuh-indexer
      - wazuh-manager
    restart: unless-stopped

  # Suricata IDS/IPS
  suricata:
    image: jasonish/suricata:latest
    container_name: trustvault-suricata
    network_mode: host
    cap_add:
      - NET_ADMIN
      - SYS_NICE
    volumes:
      - ./suricata/config:/etc/suricata
      - ./suricata/rules:/var/lib/suricata/rules
      - ./logs/suricata:/var/log/suricata
    command: >
      suricata -c /etc/suricata/suricata.yaml -i eth0 -v
    restart: unless-stopped

  # ============================================================================
  # MONITORING & METRICS
  # ============================================================================
  
  prometheus:
    image: prom/prometheus:latest
    container_name: trustvault-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - security
      - backend
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: trustvault-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    networks:
      - security
      - frontend
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped

secrets:
  django_secret:
    file: ./secrets/django_secret.txt
  db_password:
    file: ./secrets/db_password.txt
